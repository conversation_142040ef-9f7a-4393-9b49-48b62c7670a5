# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

「沉淀」是一个专注于纯净阅读体验的知识管理工具，专为需要深度阅读和知识沉淀的用户设计。它提供无干扰的内容展示，让用户能够专注于内容本身，实现真正的知识沉淀。

## 核心架构

### 技术栈
- **前端**: Next.js 15.3.3 (App Router) + React 19 + TypeScript 5
- **UI**: Tailwind CSS 2.2.9 (CDN) + Font Awesome 6.5.1 (CDN) + shadcn/ui
- **状态管理**: Zustand 5.0.5
- **数据库**: SQLite + Prisma 6.9.0
- **AI**: OpenAI GPT-4 + FastGPT 集成
- **内容处理**: @mozilla/readability + Playwright + jsdom

### 双模式架构
1. **沉淀模式** (`precipitation`): 专注于纯净阅读体验的主要模式，隐藏所有干扰元素
2. **沉思模式** (`reflection`): 知识库管理和深度思考

### 沉淀模式特性
- **纯净阅读**: 移除所有动画和干扰性悬停效果
- **专注内容**: 隐藏AI助手和编辑功能，专注于内容本身
- **玻璃效果导航**: 左侧保留轻量级的导航大纲卡片
- **优雅布局**: 简洁的单栏布局，优化阅读体验

## 开发命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint

# 数据库操作
npx prisma generate    # 生成 Prisma Client
npx prisma db push     # 推送 schema 变更到数据库
npx prisma studio      # 打开数据库管理界面
```

## 测试方法

```bash
# 测试 OpenAI API 连接
node test-connection.js

# 功能测试（使用测试 HTML 文件）
# 手动打开 test-*.html 文件进行前端测试
```

## 核心功能流程

### 内容处理流程（沉淀模式）
1. 用户输入 URL 或文本内容
2. 创建新的标签页，直接显示内容
3. 专注于纯净的内容阅读体验，无AI分析干扰

### 知识库管理（沉思模式）
1. 用户可以保存重要内容到知识库
2. 在沉思模式中管理和查看已保存的笔记
3. 提供搜索和组织功能

## 关键设计原则

1. **极简设计**: 避免过度工程化，保持界面简洁
2. **性能优先**: 使用虚拟滚动、流式处理、缓存优化
3. **用户体验**: 快速响应、流畅交互、清晰反馈
4. **本地优先**: 数据存储在本地 SQLite，保护用户隐私

## 注意事项

- **使用 CDN 版本**的 Tailwind CSS 和 Font Awesome（已在全局配置）
- **遵循现有代码风格**，查看相邻文件了解约定
- **不要创建不必要的文件**，优先编辑现有文件
- **保持代码简洁**，避免过度抽象和复杂设计
- **重视错误处理**，提供清晰的用户反馈

## 环境变量

创建 `.env.local` 文件并配置：
```env
OPENAI_API_KEY="your_openai_api_key"
DATABASE_URL="file:./prisma/dev.db"
FASTGPT_API_URL="your_fastgpt_url"  # 可选
```

## 数据模型

- **Card**: 知识卡片，存储原文和AI笔记
- **SavedNote**: 保存的笔记，包含完整对话历史
- 支持 FastGPT 同步状态追踪

## 沉淀模式设计规范

本项目专注于沉淀式阅读体验，设计原则：
- 移除所有干扰性元素，专注于内容本身
- 提供纯净的阅读环境，无动画和闪烁效果
- 保留必要的导航功能，使用玻璃效果轻量化设计
- 优化文本排版和阅读体验