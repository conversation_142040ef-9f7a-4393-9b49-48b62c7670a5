# 数据库配置
DATABASE_URL="file:./dev.db"

# OpenAI API 配置
OPENAI_API_KEY="your-openai-api-key"
OPENAI_MODEL="gpt-4o-mini"
OPENAI_TEMPERATURE="0.5"
OPENAI_MAX_TOKENS="2000"

# AI 提示词配置
AI_SYSTEM_PROMPT="你是一个专业的知识整理助手，擅长将复杂内容提炼成结构清晰、格式优美的Markdown笔记。"
STRUCTURED_NOTES_PROMPT="请将以下文章内容提炼成一份结构化的Markdown笔记。文章内容："
CHAT_SYSTEM_PROMPT="你是一个专业的AI助手，基于给定的文章内容回答用户问题。请保持回答的准确性和相关性，只基于提供的上下文内容回答，不要添加外部信息。"
REFLECTION_SYSTEM_PROMPT="你是一个拥有丰富知识库的AI助手。你可以基于知识库中的内容回答用户问题，并提供准确、有深度的回答。如果知识库中没有相关信息，请诚实说明。"

# FastGPT 基础配置
FASTGPT_API_URL="https://api.fastgpt.in"
FASTGPT_API_KEY="fastgpt-your-api-key"
FASTGPT_DATASET_ID="your-dataset-id"

# FastGPT 搜索配置 - 默认值
FASTGPT_SEARCH_DEFAULT_LIMIT="6000"
FASTGPT_SEARCH_DEFAULT_SIMILARITY="0.3"
FASTGPT_SEARCH_DEFAULT_MODE="embedding"
FASTGPT_SEARCH_DEFAULT_RERANK="false"

# FastGPT 高级搜索配置 - 沉思模式
FASTGPT_REFLECTION_SEARCH_LIMIT="4000"
FASTGPT_REFLECTION_SEARCH_SIMILARITY="0.3"
FASTGPT_REFLECTION_SEARCH_MODE="mixedRecall"
FASTGPT_REFLECTION_SEARCH_RERANK="true"
FASTGPT_REFLECTION_SEARCH_EXTENSION_QUERY="true"
FASTGPT_REFLECTION_SEARCH_EXTENSION_MODEL="gpt-4o-mini"
FASTGPT_REFLECTION_SEARCH_EXTENSION_BG="基于用户的知识库内容，优化搜索查询以获得最相关的结果"

# FastGPT 同步配置
FASTGPT_SYNC_TRAINING_TYPE="chunk"
FASTGPT_SYNC_CHUNK_SETTING_MODE="auto"
FASTGPT_SYNC_METADATA_SOURCE="knowledge-cards-app"

# FastGPT Collection管理配置  
FASTGPT_COLLECTION_LIST_LIMIT="1000"
FASTGPT_COLLECTION_NAME_MAX_LENGTH="60"

# FastGPT 搜索结果配置
FASTGPT_SEARCH_MAX_RESULTS="8"
FASTGPT_SEARCH_SHOW_SCORE="true" 
# 标题生成专用配置
TITLE_GENERATION_MODEL="gpt-3.5-turbo"
TITLE_GENERATION_PROMPT="你是一个专业的标题生成助手。请为用户提供的内容生成一个简洁、准确的标题。要求：1. 标题长度不超过10个字 2. 准确概括内容核心 3. 使用中文 4. 避免使用标点符号"
TITLE_MAX_TOKENS="100"
TITLE_TEMPERATURE="0.3"

# 推荐问题生成配置
QUESTIONS_GENERATION_PROMPT="你是一个专业的问题生成助手。基于用户提供的内容和AI笔记，生成3个有价值的推荐问题，帮助用户更深入地理解和思考内容。要求：1. 问题要具有启发性和深度 2. 问题应该能引导用户发现内容中的关键洞察 3. 避免简单的事实性问题，更多关注分析、评价、应用层面 4. 问题之间要有逻辑递进关系"

# 系统环境配置
NODE_ENV="development"
