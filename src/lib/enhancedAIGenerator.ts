import OpenAI from 'openai'
import { TextSegment } from './store'
import { locationMarkupParser } from './locationMarkupParser'

/**
 * 增强的AI生成器
 * 生成带定位标记的结构化笔记，实现精确的内容定位
 */
export class EnhancedAIGenerator {
  private openai: OpenAI

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
  }

  /**
   * 生成带定位标记的结构化笔记
   */
  async generateStructuredNotesWithLocation(
    content: string,
    title: string,
    textSegments: TextSegment[]
  ): Promise<string> {
    // 检查是否有有效的OpenAI API密钥
    const apiKey = process.env.OPENAI_API_KEY
    if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
      return this.generateMockNotesWithLocation(content, title, textSegments)
    }

    try {
      const systemPrompt = this.buildSystemPrompt(textSegments)
      const userPrompt = this.buildUserPrompt(content, title, textSegments)

      const response = await this.openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-4',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000'),
        temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7')
      })

      const generatedContent = response.choices[0]?.message?.content || ''
      
      // 验证生成的内容是否包含有效的定位标记
      if (!locationMarkupParser.hasLocationMarks(generatedContent)) {
        console.warn('AI生成的内容不包含定位标记，回退到普通模式')
        return this.generateFallbackNotes(content, title)
      }

      // 验证segment_id的有效性
      const segmentIds = locationMarkupParser.extractSegmentIds(generatedContent)
      const validIds = segmentIds.filter(id => 
        locationMarkupParser.validateSegmentId(id, textSegments)
      )

      if (validIds.length === 0) {
        console.warn('AI生成的segment_id无效，回退到普通模式')
        return this.generateFallbackNotes(content, title)
      }

      return generatedContent
    } catch (error) {
      console.error('增强AI生成失败:', error)
      return this.generateFallbackNotes(content, title)
    }
  }

  /**
   * 流式生成带定位标记的结构化笔记
   */
  async streamStructuredNotesWithLocation(
    content: string,
    title: string,
    textSegments: TextSegment[]
  ) {
    // 检查是否有有效的OpenAI API密钥
    const apiKey = process.env.OPENAI_API_KEY
    if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
      // 返回模拟的流式生成
      return this.mockStreamGeneration(content, title, textSegments)
    }

    try {
      const systemPrompt = this.buildSystemPrompt(textSegments)
      const userPrompt = this.buildUserPrompt(content, title, textSegments)

      return this.openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        stream: true,
        temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.5'),
        max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000')
      })
    } catch (error) {
      console.error('流式生成失败:', error)
      return this.mockStreamGeneration(content, title, textSegments)
    }
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(textSegments: TextSegment[]): string {
    const basePrompt = process.env.AI_SYSTEM_PROMPT || `你是一个专业的知识提炼助手，擅长将复杂内容提炼成结构清晰、格式优美的Markdown笔记。`

    const locationPrompt = `

**重要：定位标记使用规则**
1. 在生成笔记内容时，必须使用定位标记来标识内容来源
2. 定位标记格式：[LOC:segment_id]笔记内容[/LOC]
3. 可用的segment_id列表：
${textSegments.map(seg => `   - ${seg.id}: "${seg.content.substring(0, 50)}..."`).join('\n')}

4. 使用规则：
   - 每个笔记要点都应该用定位标记包围
   - 选择最相关的segment_id
   - 如果一个笔记要点涉及多个片段，选择最主要的那个
   - 确保segment_id存在于提供的列表中
   - 定位标记不会在用户界面中显示，只用于内部定位

5. 示例格式：
   ## 📋 核心观点
   - [LOC:seg_0_abc123]这是第一个核心观点的内容[/LOC]
   - [LOC:seg_1_def456]这是第二个核心观点的内容[/LOC]`

    return basePrompt + locationPrompt
  }

  /**
   * 构建用户提示词
   */
  private buildUserPrompt(content: string, title: string, textSegments: TextSegment[]): string {
    return `请为以下内容生成结构化笔记，并使用定位标记标识每个要点的来源：

**标题：** ${title}

**原文内容：**
${content}

**可用的文本片段：**
${textSegments.map((seg, index) => `${index + 1}. [${seg.id}] ${seg.content.substring(0, 100)}...`).join('\n')}

请按照以下格式生成结构化笔记，每个要点都要用定位标记包围：

## 📋 核心观点
- [LOC:相关segment_id]观点内容[/LOC]

## 🔍 关键信息  
- [LOC:相关segment_id]信息内容[/LOC]

## 💡 实用价值
- [LOC:相关segment_id]价值内容[/LOC]

## 🎯 总结
[LOC:相关segment_id]总结内容[/LOC]

请确保：
1. 每个笔记要点都用定位标记包围
2. 使用正确的segment_id
3. 保持内容的准确性和可读性`
  }

  /**
   * 生成模拟的带定位标记的笔记
   */
  private generateMockNotesWithLocation(
    content: string,
    title: string,
    textSegments: TextSegment[]
  ): string {
    const availableSegments = textSegments.slice(0, Math.min(5, textSegments.length))
    
    return `# ${title}

## 📋 核心观点
- [LOC:${availableSegments[0]?.id || 'seg_0'}]这是基于原文内容提炼的核心观点[/LOC]
- [LOC:${availableSegments[1]?.id || 'seg_1'}]内容长度：${content.length} 字符，展示了丰富的信息量[/LOC]
- [LOC:${availableSegments[2]?.id || 'seg_2'}]生成时间：${new Date().toLocaleString('zh-CN')}，确保信息的时效性[/LOC]

## 🔍 关键信息
- [LOC:${availableSegments[0]?.id || 'seg_0'}]**主要内容**：${content.substring(0, 100)}${content.length > 100 ? '...' : ''}[/LOC]
- [LOC:${availableSegments[1]?.id || 'seg_1'}]**内容类型**：文本分析，支持多种格式处理[/LOC]
- [LOC:${availableSegments[2]?.id || 'seg_2'}]**处理状态**：演示模式，展示定位功能[/LOC]

## 💡 实用价值
- [LOC:${availableSegments[3]?.id || 'seg_3'}]📝 **知识价值**：中等，适合学习和参考[/LOC]
- [LOC:${availableSegments[4]?.id || 'seg_4'}]🎯 **实用性**：演示功能，展示智能定位能力[/LOC]
- [LOC:${availableSegments[0]?.id || 'seg_0'}]💡 **启发性**：展示结构化笔记与原文的精确关联[/LOC]

## 🎯 总结
[LOC:${availableSegments[0]?.id || 'seg_0'}]这是一个演示用的带定位标记的结构化笔记，展示了AI如何将笔记内容与原文片段建立精确的映射关系。[/LOC]

> 💡 **提示**：这是演示模式生成的笔记。要获得真正的智能分析，请配置有效的OpenAI API密钥。`
  }

  /**
   * 生成回退笔记（不带定位标记）
   */
  private async generateFallbackNotes(content: string, title: string): Promise<string> {
    return `# ${title}

## 📋 核心观点
- 内容分析和提炼
- 结构化信息整理
- 知识要点总结

## 🔍 关键信息
- **内容长度**：${content.length} 字符
- **处理时间**：${new Date().toLocaleString('zh-CN')}
- **内容预览**：${content.substring(0, 200)}...

## 💡 实用价值
- 📝 **知识整理**：系统化的信息组织
- 🎯 **快速理解**：结构清晰的内容呈现
- 💡 **深度思考**：促进知识的深入理解

## 🎯 总结
这是一份结构化的内容笔记，帮助更好地理解和记忆核心信息。`
  }

  /**
   * 模拟流式生成
   */
  private async *mockStreamGeneration(
    content: string,
    title: string,
    textSegments: TextSegment[]
  ) {
    const mockContent = this.generateMockNotesWithLocation(content, title, textSegments)
    const chunks = mockContent.split(' ')
    
    for (let i = 0; i < chunks.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 50)) // 模拟延迟
      
      yield {
        choices: [{
          delta: {
            content: chunks[i] + (i < chunks.length - 1 ? ' ' : '')
          }
        }]
      }
    }
  }
}

export const enhancedAIGenerator = new EnhancedAIGenerator()
