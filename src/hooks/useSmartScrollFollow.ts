import { useRef, useEffect, useCallback, useState } from 'react'

// 简化的配置
const DEFAULT_CONFIG = {
  scrollFollow: {
    threshold: 50,
    debounceMs: 150,
    minScrollDelta: 10
  },
  manualOverride: {
    duration: 2000
  },
  animation: {
    smoothScroll: true
  },
  performance: {
    contentChangeScrollDelay: 50
  }
}

interface ScrollFollowOptions {
  enabled?: boolean
  threshold?: number // 距离底部多少像素时认为在底部
  smoothScroll?: boolean
  debounceMs?: number
  manualOverrideDuration?: number // 手动操作后的覆盖时间
  minScrollDelta?: number // 最小滚动距离才认为是用户滚动
}

interface ScrollFollowState {
  isUserScrolling: boolean
  isAtBottom: boolean
  shouldAutoFollow: boolean
  lastScrollTop: number
  lastScrollTime: number
  showScrollToBottomButton: boolean
  manualOverrideUntil: number
}

export const useSmartScrollFollow = (options: ScrollFollowOptions = {}) => {
  const {
    enabled = true,
    threshold = options.threshold ?? DEFAULT_CONFIG.scrollFollow.threshold,
    smoothScroll = options.smoothScroll ?? DEFAULT_CONFIG.animation.smoothScroll,
    debounceMs = options.debounceMs ?? DEFAULT_CONFIG.scrollFollow.debounceMs,
    manualOverrideDuration = options.manualOverrideDuration ?? DEFAULT_CONFIG.manualOverride.duration,
    minScrollDelta = options.minScrollDelta ?? DEFAULT_CONFIG.scrollFollow.minScrollDelta
  } = options

  const containerRef = useRef<HTMLDivElement>(null)
  const scrollTimeoutRef = useRef<NodeJS.Timeout>()
  const isAutoScrollingRef = useRef(false)
  const lastContentHeightRef = useRef(0)

  const [scrollState, setScrollState] = useState<ScrollFollowState>({
    isUserScrolling: false,
    isAtBottom: true,
    shouldAutoFollow: true,
    lastScrollTop: 0,
    lastScrollTime: 0,
    showScrollToBottomButton: false,
    manualOverrideUntil: 0
  })

  // 检查是否在底部
  const checkIsAtBottom = useCallback((element: HTMLElement): boolean => {
    const { scrollTop, scrollHeight, clientHeight } = element
    return scrollHeight - scrollTop - clientHeight <= threshold
  }, [threshold])

  // 平滑滚动到底部
  const scrollToBottom = useCallback((force = false) => {
    const container = containerRef.current
    if (!container || (!enabled && !force)) return

    isAutoScrollingRef.current = true
    
    if (smoothScroll) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      })
    } else {
      container.scrollTop = container.scrollHeight
    }

    // 重置自动滚动标记
    setTimeout(() => {
      isAutoScrollingRef.current = false
    }, 500)
  }, [enabled, smoothScroll])

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    const container = containerRef.current
    if (!container || isAutoScrollingRef.current) return

    const now = Date.now()
    const { scrollTop, scrollHeight, clientHeight } = container
    const isAtBottom = checkIsAtBottom(container)

    // 检测用户主动滚动
    const scrollDelta = Math.abs(scrollTop - scrollState.lastScrollTop)
    const isUserScrolling = scrollDelta > minScrollDelta
    
    setScrollState(prev => {
      // 如果用户主动滚动，设置手动覆盖时间
      const manualOverrideUntil = isUserScrolling ? now + manualOverrideDuration : prev.manualOverrideUntil
      
      // 如果在手动覆盖期间，保持原有的跟随状态
      const shouldAutoFollow = now < manualOverrideUntil ? prev.shouldAutoFollow : isAtBottom
      
      const newState = {
        ...prev,
        isUserScrolling,
        isAtBottom,
        shouldAutoFollow,
        lastScrollTop: scrollTop,
        lastScrollTime: now,
        showScrollToBottomButton: !isAtBottom && enabled,
        manualOverrideUntil
      }
      return newState
    })

    // 清除之前的超时
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // 设置防抖，在用户停止滚动后重新评估状态
    scrollTimeoutRef.current = setTimeout(() => {
      setScrollState(prev => ({
        ...prev,
        isUserScrolling: false
      }))
    }, debounceMs)
  }, [checkIsAtBottom, scrollState.lastScrollTop, minScrollDelta, manualOverrideDuration, enabled, debounceMs])

  // 内容变化时的自动滚动
  const handleContentChange = useCallback(() => {
    const container = containerRef.current
    if (!container || !enabled) return

    const now = Date.now()
    const currentHeight = container.scrollHeight
    const heightChanged = currentHeight !== lastContentHeightRef.current
    
    // 检查是否在手动覆盖期间
    const isInManualOverride = now < scrollState.manualOverrideUntil
    
    if (heightChanged && scrollState.shouldAutoFollow && !scrollState.isUserScrolling && !isInManualOverride) {
      // 使用 requestAnimationFrame 和延迟确保 DOM 更新完成后再滚动
      setTimeout(() => {
        requestAnimationFrame(() => {
          scrollToBottom()
        })
      }, DEFAULT_CONFIG.performance.contentChangeScrollDelay)
    }

    lastContentHeightRef.current = currentHeight
  }, [enabled, scrollState.shouldAutoFollow, scrollState.isUserScrolling, scrollState.manualOverrideUntil, scrollToBottom])

  // 设置滚动监听
  useEffect(() => {
    const container = containerRef.current
    if (!container || !enabled) return

    container.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      container.removeEventListener('scroll', handleScroll)
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [handleScroll, enabled])

  // 监听内容变化
  useEffect(() => {
    const container = containerRef.current
    if (!container || !enabled) return

    const observer = new MutationObserver(() => {
      handleContentChange()
    })

    observer.observe(container, {
      childList: true,
      subtree: true,
      characterData: true
    })

    return () => {
      observer.disconnect()
    }
  }, [handleContentChange, enabled])

  // 强制滚动到底部
  const forceScrollToBottom = useCallback(() => {
    scrollToBottom(true)
    const now = Date.now()
    setScrollState(prev => ({
      ...prev,
      shouldAutoFollow: true,
      isAtBottom: true,
      showScrollToBottomButton: false,
      manualOverrideUntil: 0 // 清除手动覆盖
    }))
  }, [scrollToBottom])

  // 暂停自动跟随
  const pauseAutoFollow = useCallback(() => {
    const now = Date.now()
    setScrollState(prev => ({
      ...prev,
      shouldAutoFollow: false,
      manualOverrideUntil: now + manualOverrideDuration
    }))
  }, [manualOverrideDuration])

  // 恢复自动跟随
  const resumeAutoFollow = useCallback(() => {
    setScrollState(prev => ({
      ...prev,
      shouldAutoFollow: true,
      manualOverrideUntil: 0
    }))
  }, [])

  // 设置手动操作标记
  const setManualOverride = useCallback(() => {
    const now = Date.now()
    setScrollState(prev => ({
      ...prev,
      manualOverrideUntil: now + manualOverrideDuration
    }))
  }, [manualOverrideDuration])

  return {
    containerRef,
    scrollState,
    forceScrollToBottom,
    pauseAutoFollow,
    resumeAutoFollow,
    setManualOverride,
    scrollToBottom: () => scrollToBottom(false),
    // 便捷属性
    isAtBottom: scrollState.isAtBottom,
    showScrollButton: scrollState.showScrollToBottomButton,
    isUserScrolling: scrollState.isUserScrolling,
    shouldAutoFollow: scrollState.shouldAutoFollow,
    // 性能相关 - 简化版本
    isPerformanceGood: true
  }
}
