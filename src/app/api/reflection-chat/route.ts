import { NextRequest, NextResponse } from 'next/server'
import { searchKnowledgeBase, getReflectionSearchOptions, formatSearchResults } from '@/lib/fastgpt'
import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
})

export async function POST(request: NextRequest) {
  try {
    const { message, context, history } = await request.json()

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      )
    }

    // 创建流式响应
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // 1. 从FastGPT知识库搜索相关内容
          let kbContext = ''
          let hasKnowledgeBase = false
          
          try {
            const searchOptions = getReflectionSearchOptions()
            const kbResults = await searchKnowledgeBase(message, searchOptions)
            
            if (Array.isArray(kbResults) && kbResults.length > 0) {
              hasKnowledgeBase = true
              kbContext = formatSearchResults(kbResults)
            }
          } catch (error) {
            console.error('[Reflection Mode] Knowledge base search failed:', error)
          }

          // 2. 构建系统提示词
          const systemPrompt = process.env.REFLECTION_SYSTEM_PROMPT || `你是一个专业的AI助手，能够：
1. 基于用户提供的笔记内容进行深度分析和讨论
2. 结合知识库中的相关信息提供全面的回答
3. 保持对话的连贯性和上下文关联

请提供有价值、有深度的回答，帮助用户更好地理解和应用知识。`

          // 3. 构建上下文信息
          let fullContext = ''
          
          if (context) {
            fullContext += `当前笔记内容：
标题：${context.title || ''}
原文：${context.originalContent || ''}
结构化笔记：${context.structuredNotes || ''}
整合笔记：${context.integratedNotes || ''}

`
          }

          if (hasKnowledgeBase) {
            fullContext += `知识库检索结果：
${kbContext}

`
          }

          // 4. 构建消息数组
          const messages: Array<{role: 'system' | 'user' | 'assistant', content: string}> = [
            {
              role: "system",
              content: systemPrompt + (fullContext ? `\n\n参考信息：\n${fullContext}` : '')
            }
          ]

          // 添加对话历史（最多保留最近6轮对话）
          if (history && Array.isArray(history)) {
            const recentHistory = history.slice(-6)
            for (const msg of recentHistory) {
              if (msg.role === 'user' || msg.role === 'assistant') {
                messages.push({
                  role: msg.role,
                  content: msg.content
                })
              }
            }
          }

          // 添加当前问题
          messages.push({
            role: "user",
            content: message
          })

          // 5. 调用OpenAI API进行流式响应
          const completion = await openai.chat.completions.create({
            model: process.env.OPENAI_MODEL || "gpt-4o-mini",
            messages,
            stream: true,
            temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.4'),
            max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '1500')
          })

          // 6. 处理流式响应
          for await (const chunk of completion) {
            const content = chunk.choices[0]?.delta?.content || ''
            if (content) {
              const data = JSON.stringify({
                choices: [{
                  delta: {
                    content
                  }
                }]
              })
              controller.enqueue(new TextEncoder().encode(`data: ${data}\n\n`))
            }
          }

          // 7. 添加知识库使用说明
          if (hasKnowledgeBase) {
            const footer = "\n\n---\n*此回答结合了知识库内容*"
            const footerData = JSON.stringify({
              choices: [{
                delta: {
                  content: footer
                }
              }]
            })
            controller.enqueue(new TextEncoder().encode(`data: ${footerData}\n\n`))
          }

          // 结束流
          controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
          controller.close()

        } catch (error) {
          console.error('Reflection chat streaming error:', error)
          const errorData = JSON.stringify({
            choices: [{
              delta: {
                content: '抱歉，处理您的问题时发生了错误。请稍后重试。'
              }
            }]
          })
          controller.enqueue(new TextEncoder().encode(`data: ${errorData}\n\n`))
          controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
          controller.close()
        }
      }
    })

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {
    console.error('Reflection chat API error:', error)
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    )
  }
} 