import { NextResponse } from 'next/server'
import { getDefaultSearchOptions, getReflectionSearchOptions } from '@/lib/fastgpt'

/**
 * 获取当前FastGPT配置
 */
export async function GET() {
  try {
    const config = {
      // 基础配置状态
      isConfigured: !!(
        process.env.FASTGPT_API_URL && 
        process.env.FASTGPT_API_KEY && 
        process.env.FASTGPT_DATASET_ID
      ),
      
      // 基础配置
      apiUrl: process.env.FASTGPT_API_URL || '',
      datasetId: process.env.FASTGPT_DATASET_ID || '',
      hasApiKey: !!process.env.FASTGPT_API_KEY,
      
      // 搜索配置
      defaultSearch: getDefaultSearchOptions(),
      reflectionSearch: getReflectionSearchOptions(),
      
      // 同步配置
      sync: {
        trainingType: process.env.FASTGPT_SYNC_TRAINING_TYPE || 'chunk',
        chunkSettingMode: process.env.FASTGPT_SYNC_CHUNK_SETTING_MODE || 'auto',
        metadataSource: process.env.FASTGPT_SYNC_METADATA_SOURCE || 'knowledge-cards-app'
      },
      
      // Collection管理配置
      collection: {
        listLimit: parseInt(process.env.FASTGPT_COLLECTION_LIST_LIMIT || '1000'),
        nameMaxLength: parseInt(process.env.FASTGPT_COLLECTION_NAME_MAX_LENGTH || '60')
      },
      
      // 搜索结果配置
      searchResults: {
        maxResults: parseInt(process.env.FASTGPT_SEARCH_MAX_RESULTS || '8'),
        showScore: process.env.FASTGPT_SEARCH_SHOW_SCORE === 'true'
      }
    }

    return NextResponse.json(config)
  } catch (error) {
    console.error('[FastGPT Config] Error:', error)
    return NextResponse.json(
      { error: 'Failed to get FastGPT configuration' },
      { status: 500 }
    )
  }
}

/**
 * 验证FastGPT配置是否正确
 */
export async function POST() {
  try {
    const baseUrl = process.env.FASTGPT_API_URL?.replace(/\/$/, '')
    const apiKey = process.env.FASTGPT_API_KEY
    const datasetId = process.env.FASTGPT_DATASET_ID

    if (!baseUrl || !apiKey || !datasetId) {
      return NextResponse.json({
        valid: false,
        error: 'Missing required environment variables',
        missing: {
          apiUrl: !baseUrl,
          apiKey: !apiKey,
          datasetId: !datasetId
        }
      })
    }

    // 测试API连接
    try {
      const testResponse = await fetch(`${baseUrl}/api/core/dataset/collection/list`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          datasetId,
          parentId: null,
          searchText: '',
          limit: 1
        })
      })

      if (!testResponse.ok) {
        return NextResponse.json({
          valid: false,
          error: `API connection failed: HTTP ${testResponse.status}`,
          httpStatus: testResponse.status
        })
      }

      const result = await testResponse.json()
      if (result.code !== 200) {
        return NextResponse.json({
          valid: false,
          error: result.message || 'FastGPT API error',
          fastgptCode: result.code
        })
      }

      return NextResponse.json({
        valid: true,
        message: 'FastGPT configuration is valid',
        collectionsFound: result.data?.collections?.length || 0
      })

    } catch (fetchError) {
      return NextResponse.json({
        valid: false,
        error: `Network error: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`
      })
    }

  } catch (error) {
    console.error('[FastGPT Config Validation] Error:', error)
    return NextResponse.json(
      { error: 'Failed to validate FastGPT configuration' },
      { status: 500 }
    )
  }
} 