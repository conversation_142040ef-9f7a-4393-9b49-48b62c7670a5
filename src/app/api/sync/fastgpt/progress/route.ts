import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getCollectionStatus } from '@/lib/fastgpt'

export async function GET() {
  try {
    // 获取所有已同步的笔记
    const syncedNotes = await prisma.savedNote.findMany({
      where: {
        fastgptSyncStatus: 'synced',
        fastgptCollectionId: { not: null }
      },
      select: {
        id: true,
        title: true,
        fastgptCollectionId: true,
        fastgptTrainingStatus: true,
        fastgptLastSyncAt: true
      }
    })

    const progressResults = []

    // 检查每个collection的训练状态
    for (const note of syncedNotes) {
      if (note.fastgptCollectionId) {
        try {
          const status = await getCollectionStatus(note.fastgptCollectionId)
          
          if (status) {
            const currentStatus = status.status
            
            // 如果状态发生变化，更新数据库
            if (currentStatus !== note.fastgptTrainingStatus) {
              await prisma.savedNote.update({
                where: { id: note.id },
                data: { fastgptTrainingStatus: currentStatus }
              })
            }

            progressResults.push({
              noteId: note.id,
              title: note.title,
              collectionId: note.fastgptCollectionId,
              trainingStatus: currentStatus,
              lastSyncAt: note.fastgptLastSyncAt,
              updateTime: status.updateTime
            })
          } else {
            progressResults.push({
              noteId: note.id,
              title: note.title,
              collectionId: note.fastgptCollectionId,
              trainingStatus: 'unknown',
              lastSyncAt: note.fastgptLastSyncAt,
              error: 'Failed to get status from FastGPT'
            })
          }
        } catch (error) {
          progressResults.push({
            noteId: note.id,
            title: note.title,
            collectionId: note.fastgptCollectionId,
            trainingStatus: 'error',
            lastSyncAt: note.fastgptLastSyncAt,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }
    }

    // 统计训练状态
    const statusStats = progressResults.reduce((acc, result) => {
      const status = result.trainingStatus
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      total: progressResults.length,
      statusStats,
      details: progressResults
    })

  } catch (error) {
    console.error('[Training Progress] Error:', error)
    return NextResponse.json(
      { error: 'Failed to get training progress' },
      { status: 500 }
    )
  }
} 