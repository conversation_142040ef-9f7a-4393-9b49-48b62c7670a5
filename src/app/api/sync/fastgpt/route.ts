import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { syncNoteToFastGPT, getCollectionStatus } from '@/lib/fastgpt'

export async function POST() {
  try {
    // 获取所有未同步或同步失败的笔记
    const notes = await prisma.savedNote.findMany({
      where: {
        OR: [
          { fastgptSyncStatus: 'pending' },
          { fastgptSyncStatus: 'failed' },
          { fastgptCollectionId: null }
        ]
      }
    })

    let success = 0
    let failed = 0
    const results = []

    for (const note of notes) {
      try {
        // 更新状态为同步中
        await prisma.savedNote.update({
          where: { id: note.id },
          data: { 
            fastgptSyncStatus: 'syncing',
            fastgptErrorMessage: null
          }
        })

        // 执行同步
        const collectionId = await syncNoteToFastGPT(note as any)
        
        if (collectionId) {
          // 同步成功，更新数据库
          await prisma.savedNote.update({
            where: { id: note.id },
            data: {
              fastgptCollectionId: collectionId,
              fastgptSyncStatus: 'synced',
              fastgptTrainingStatus: 'waiting',
              fastgptLastSyncAt: new Date(),
              fastgptErrorMessage: null
            }
          })
          success += 1
          results.push({ id: note.id, status: 'success', collectionId })
        } else {
          // 同步失败
          await prisma.savedNote.update({
            where: { id: note.id },
            data: {
              fastgptSyncStatus: 'failed',
              fastgptErrorMessage: 'Failed to create collection in FastGPT'
            }
          })
          failed += 1
          results.push({ id: note.id, status: 'failed', error: 'Sync failed' })
        }
      } catch (error) {
        // 处理单个笔记同步错误
        await prisma.savedNote.update({
          where: { id: note.id },
          data: {
            fastgptSyncStatus: 'failed',
            fastgptErrorMessage: error instanceof Error ? error.message : 'Unknown error'
          }
        })
        failed += 1
        results.push({ 
          id: note.id, 
          status: 'failed', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        })
      }
    }

    return NextResponse.json({ 
      success, 
      failed, 
      total: notes.length,
      results 
    })
  } catch (error) {
    console.error('[FastGPT Sync] Error:', error)
    return NextResponse.json(
      { error: 'FastGPT sync failed' },
      { status: 500 }
    )
  }
}

/**
 * 获取同步状态
 */
export async function GET() {
  try {
    const stats = await prisma.savedNote.groupBy({
      by: ['fastgptSyncStatus'],
      _count: true
    })

    const statusCounts = stats.reduce((acc, stat) => {
      acc[stat.fastgptSyncStatus] = stat._count
      return acc
    }, {} as Record<string, number>)

    const totalNotes = await prisma.savedNote.count()
    const syncedNotes = statusCounts['synced'] || 0
    const pendingNotes = statusCounts['pending'] || 0
    const failedNotes = statusCounts['failed'] || 0
    const syncingNotes = statusCounts['syncing'] || 0

    return NextResponse.json({
      total: totalNotes,
      synced: syncedNotes,
      pending: pendingNotes,
      failed: failedNotes,
      syncing: syncingNotes,
      syncProgress: totalNotes > 0 ? (syncedNotes / totalNotes * 100).toFixed(1) : '0'
    })
  } catch (error) {
    console.error('[FastGPT Sync Status] Error:', error)
    return NextResponse.json(
      { error: 'Failed to get sync status' },
      { status: 500 }
    )
  }
} 