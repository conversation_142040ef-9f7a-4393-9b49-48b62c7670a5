import { NextRequest, NextResponse } from 'next/server'
import { askQuestionWithKnowledgeBase } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const { question } = await request.json()

    if (!question) {
      return NextResponse.json(
        { error: 'Question is required' },
        { status: 400 }
      )
    }

    // 沉思模式：使用FastGPT知识库进行问答
    // 注意：沉淀模式应该使用 /api/chat-with-document 而不是这个接口
    const answer = await askQuestionWithKnowledgeBase(question)

    return NextResponse.json({ answer })
  } catch (error) {
    console.error('Chat API error:', error)
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    )
  }
}
