import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { deleteFromFastGPT } from '@/lib/fastgpt'

// 获取单个笔记详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const note = await prisma.savedNote.findUnique({
      where: { id: params.id }
    })

    if (!note) {
      return NextResponse.json(
        { error: '笔记不存在' },
        { status: 404 }
      )
    }

    // 处理标签
    const processedNote = {
      ...note,
      tags: note.tags ? JSON.parse(note.tags) : []
    }

    return NextResponse.json(processedNote)

  } catch (error) {
    console.error('Error fetching note:', error)
    return NextResponse.json(
      { error: '获取笔记失败' },
      { status: 500 }
    )
  }
}

// 更新笔记
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const {
      title,
      integratedNotes,
      tags
    } = await request.json()

    const updatedNote = await prisma.savedNote.update({
      where: { id: params.id },
      data: {
        title,
        integratedNotes,
        tags: tags ? JSON.stringify(tags) : null,
        updatedAt: new Date()
      }
    })

    const processedNote = {
      ...updatedNote,
      tags: updatedNote.tags ? JSON.parse(updatedNote.tags) : []
    }

    return NextResponse.json({
      success: true,
      note: processedNote
    })

  } catch (error) {
    console.error('Error updating note:', error)
    return NextResponse.json(
      { error: '更新笔记失败' },
      { status: 500 }
    )
  }
}

// 删除笔记
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 首先查找笔记，获取FastGPT collection信息
    const note = await prisma.savedNote.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        title: true,
        fastgptCollectionId: true,
        fastgptSyncStatus: true
      }
    })

    if (!note) {
      return NextResponse.json(
        { error: '笔记不存在' },
        { status: 404 }
      )
    }

    let fastgptDeleted = false
    let fastgptError = null

    // 如果笔记已同步到FastGPT，先从FastGPT删除
    if (note.fastgptCollectionId && note.fastgptSyncStatus === 'synced') {
      try {
        console.log(`[Delete] Deleting FastGPT collection: ${note.fastgptCollectionId}`)
        fastgptDeleted = await deleteFromFastGPT(note.fastgptCollectionId)
        if (!fastgptDeleted) {
          console.warn(`[Delete] Failed to delete FastGPT collection: ${note.fastgptCollectionId}`)
          fastgptError = 'FastGPT deletion failed'
        } else {
          console.log(`[Delete] Successfully deleted FastGPT collection: ${note.fastgptCollectionId}`)
        }
      } catch (error) {
        console.error(`[Delete] Error deleting from FastGPT:`, error)
        fastgptError = error instanceof Error ? error.message : 'Unknown FastGPT error'
      }
    } else {
      console.log(`[Delete] Note not synced to FastGPT, skipping remote deletion`)
    }

    // 删除本地笔记
    await prisma.savedNote.delete({
      where: { id: params.id }
    })

    console.log(`[Delete] Successfully deleted local note: ${note.title}`)

    // 返回详细的删除结果
    const response = {
      success: true,
      message: '笔记已删除',
      localDeleted: true,
      fastgptDeleted,
      fastgptError,
      details: {
        noteTitle: note.title,
        hadFastGPTCollection: !!note.fastgptCollectionId,
        wasSynced: note.fastgptSyncStatus === 'synced'
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error deleting note:', error)
    return NextResponse.json(
      { 
        error: '删除笔记失败',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
