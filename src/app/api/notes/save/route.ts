import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { syncNoteToFastGPT } from '@/lib/fastgpt'

export async function POST(request: NextRequest) {
  try {
    const {
      title,
      originalContent,
      structuredNotes,
      integratedNotes,
      sourceType,
      sourceData,
      tags
    } = await request.json()

    // 验证必需字段
    if (!title || !originalContent || !integratedNotes || !sourceType || !sourceData) {
      return NextResponse.json(
        { error: '缺少必需字段' },
        { status: 400 }
      )
    }

    // 保存笔记到数据库
    const savedNote = await prisma.savedNote.create({
      data: {
        title,
        originalContent,
        structuredNotes: structuredNotes || '',
        integratedNotes,
        sourceType,
        sourceData,
        tags: tags ? JSON.stringify(tags) : null,
        fastgptSyncStatus: 'pending' // 初始状态设为待同步
      }
    })

    // 自动同步到 FastGPT (异步执行，不阻塞响应)
    syncToFastGPTAsync(savedNote)

    return NextResponse.json({
      success: true,
      note: savedNote
    })

  } catch (error) {
    console.error('Error saving note:', error)
    return NextResponse.json(
      { error: '保存笔记失败' },
      { status: 500 }
    )
  }
}

/**
 * 异步同步笔记到 FastGPT，不阻塞主流程
 */
async function syncToFastGPTAsync(note: any) {
  try {
    // 更新状态为同步中
    await prisma.savedNote.update({
      where: { id: note.id },
      data: { 
        fastgptSyncStatus: 'syncing',
        fastgptErrorMessage: null
      }
    })

    // 执行同步
    const collectionId = await syncNoteToFastGPT(note)
    
    if (collectionId) {
      // 同步成功
      await prisma.savedNote.update({
        where: { id: note.id },
        data: {
          fastgptCollectionId: collectionId,
          fastgptSyncStatus: 'synced',
          fastgptTrainingStatus: 'waiting',
          fastgptLastSyncAt: new Date(),
          fastgptErrorMessage: null
        }
      })
      console.log(`[AutoSync] Note ${note.id} synced successfully to FastGPT: ${collectionId}`)
    } else {
      // 同步失败
      await prisma.savedNote.update({
        where: { id: note.id },
        data: {
          fastgptSyncStatus: 'failed',
          fastgptErrorMessage: 'Failed to create collection in FastGPT'
        }
      })
      console.warn(`[AutoSync] Note ${note.id} sync failed`)
    }
  } catch (error) {
    // 处理同步错误
    try {
      await prisma.savedNote.update({
        where: { id: note.id },
        data: {
          fastgptSyncStatus: 'failed',
          fastgptErrorMessage: error instanceof Error ? error.message : 'Unknown sync error'
        }
      })
    } catch (dbError) {
      console.error('[AutoSync] Failed to update sync error status:', dbError)
    }
    console.error(`[AutoSync] Note ${note.id} sync error:`, error)
  }
}
