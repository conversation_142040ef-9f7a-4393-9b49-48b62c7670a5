'use client'

import React from 'react'
import { useSelectedNote } from '@/lib/store'
import { Globe, Type, Calendar, Tag, ExternalLink } from 'lucide-react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'

const NoteDetail: React.FC = () => {
  const selectedNote = useSelectedNote()

  if (!selectedNote) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Type className="w-8 h-8 text-gray-400" />
          </div>
          <p className="text-gray-500">选择一个笔记查看详情</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-6 border-b border-gray-200/50">
        <div className="flex items-start justify-between mb-4">
          <h1 className="text-xl font-bold text-gray-900 flex-1">{selectedNote.title}</h1>
        </div>

        {/* 元信息 */}
        <div className="flex items-center space-x-4 text-sm text-gray-500">
          {/* 来源类型 */}
          <div className="flex items-center space-x-1">
            {selectedNote.sourceType === 'url' ? (
              <Globe className="w-4 h-4" />
            ) : (
              <Type className="w-4 h-4" />
            )}
            <span>{selectedNote.sourceType === 'url' ? '网页' : '文本'}</span>
          </div>

          {/* 创建时间 */}
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4" />
            <span>{new Date(selectedNote.createdAt).toLocaleString('zh-CN')}</span>
          </div>

          {/* 来源链接 */}
          {selectedNote.sourceType === 'url' && (
            <a
              href={selectedNote.sourceData}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
              <span>查看原文</span>
            </a>
          )}
        </div>

        {/* 标签 */}
        {selectedNote.tags && selectedNote.tags.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-2">
            {selectedNote.tags.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
              >
                <Tag className="w-3 h-3 mr-1" />
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="prose prose-lg max-w-none prose-headings:text-gray-800 prose-headings:font-semibold prose-p:text-gray-700 prose-p:leading-relaxed prose-ul:text-gray-700 prose-ol:text-gray-700 prose-li:my-1 prose-strong:text-gray-900 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:rounded prose-blockquote:border-l-4 prose-blockquote:border-blue-200 prose-blockquote:pl-4 prose-blockquote:italic prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline prose-p:break-words prose-p:whitespace-pre-wrap">
          <SafeMarkdown>{selectedNote.originalContent}</SafeMarkdown>
        </div>
      </div>
    </div>
  )
}

export default NoteDetail
