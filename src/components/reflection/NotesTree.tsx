'use client'

import React, { useState, useMemo } from 'react'
import { useAppStore, useFilteredNotes } from '@/lib/store'
import { Search, FileText, Globe, Type, Calendar, Tag, Trash2 } from 'lucide-react'
import ModernLoader from '@/components/ui/ModernLoader'

const NotesTree: React.FC = () => {
  const { 
    notesSearchQuery, 
    setNotesSearchQuery, 
    selectedNoteId, 
    selectNote, 
    notesLoading,
    deleteNote 
  } = useAppStore()
  const filteredNotes = useFilteredNotes()
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null)

  // 按日期分组笔记
  const groupedNotes = useMemo(() => {
    const groups: Record<string, typeof filteredNotes> = {}
    
    filteredNotes.forEach(note => {
      const date = new Date(note.createdAt)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      
      let groupKey: string
      if (date.toDateString() === today.toDateString()) {
        groupKey = '今天'
      } else if (date.toDateString() === yesterday.toDateString()) {
        groupKey = '昨天'
      } else {
        groupKey = date.toLocaleDateString('zh-CN', { 
          year: 'numeric', 
          month: 'long' 
        })
      }
      
      if (!groups[groupKey]) {
        groups[groupKey] = []
      }
      groups[groupKey].push(note)
    })
    
    return groups
  }, [filteredNotes])

  const handleDeleteNote = async (noteId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (deleteConfirm === noteId) {
      try {
        await deleteNote(noteId)
        setDeleteConfirm(null)
        console.log('笔记删除成功')
      } catch (error) {
        console.error('删除笔记失败:', error)
        alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`)
        setDeleteConfirm(null)
      }
    } else {
      setDeleteConfirm(noteId)
      // 3秒后自动取消确认状态
      setTimeout(() => setDeleteConfirm(null), 3000)
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-6 border-b border-gray-200/50">
        <h2 className="text-xl font-bold text-gray-900 mb-4">知识库</h2>
        
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索笔记..."
            value={notesSearchQuery}
            onChange={(e) => setNotesSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-colors"
          />
        </div>
      </div>

      {/* 笔记列表 */}
      <div className="flex-1 overflow-y-auto">
        {notesLoading ? (
          <div className="p-6">
            <ModernLoader variant="dots" size="md" text="加载笔记中..." className="text-center" />
          </div>
        ) : Object.keys(groupedNotes).length === 0 ? (
          <div className="p-6 text-center">
            <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <FileText className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-500 text-sm">
              {notesSearchQuery ? '没有找到匹配的笔记' : '还没有保存的笔记'}
            </p>
          </div>
        ) : (
          <div className="p-4 space-y-6">
            {Object.entries(groupedNotes).map(([groupName, notes]) => (
              <div key={groupName}>
                {/* 分组标题 */}
                <div className="flex items-center space-x-2 mb-3">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <h3 className="text-sm font-medium text-gray-600">{groupName}</h3>
                  <div className="flex-1 h-px bg-gray-200"></div>
                </div>

                {/* 笔记列表 */}
                <div className="space-y-2">
                  {notes.map((note) => (
                    <div
                      key={note.id}
                      onClick={() => selectNote(note.id)}
                      className={`group relative p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedNoteId === note.id
                          ? 'bg-blue-50 border border-blue-200 shadow-sm'
                          : 'hover:bg-gray-50 border border-transparent'
                      }`}
                    >
                      {/* 笔记内容 */}
                      <div className="flex items-start space-x-3">
                        {/* 来源图标 */}
                        <div className={`p-1.5 rounded-lg flex-shrink-0 ${
                          note.sourceType === 'url' 
                            ? 'bg-blue-100 text-blue-600' 
                            : 'bg-green-100 text-green-600'
                        }`}>
                          {note.sourceType === 'url' ? (
                            <Globe className="w-3 h-3" />
                          ) : (
                            <Type className="w-3 h-3" />
                          )}
                        </div>

                        {/* 笔记信息 */}
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 text-sm truncate mb-1">
                            {note.title}
                          </h4>
                          <p className="text-xs text-gray-500 line-clamp-2 mb-2">
                            {note.preview}
                          </p>
                          
                          {/* 标签 */}
                          {note.tags && note.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {note.tags.slice(0, 2).map((tag, index) => (
                                <span
                                  key={index}
                                  className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-gray-100 text-gray-600"
                                >
                                  <Tag className="w-2 h-2 mr-1" />
                                  {tag}
                                </span>
                              ))}
                              {note.tags.length > 2 && (
                                <span className="text-xs text-gray-400">
                                  +{note.tags.length - 2}
                                </span>
                              )}
                            </div>
                          )}
                        </div>

                        {/* 删除按钮 */}
                        <button
                          onClick={(e) => handleDeleteNote(note.id, e)}
                          className={`opacity-0 group-hover:opacity-100 p-1 rounded transition-all duration-200 ${
                            deleteConfirm === note.id
                              ? 'bg-red-100 text-red-600'
                              : 'hover:bg-gray-100 text-gray-400 hover:text-red-500'
                          }`}
                          title={deleteConfirm === note.id ? '确认删除' : '删除笔记'}
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>

                      {/* 时间戳 */}
                      <div className="mt-2 text-xs text-gray-400">
                        {new Date(note.createdAt).toLocaleString('zh-CN')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export { NotesTree }
export default NotesTree
