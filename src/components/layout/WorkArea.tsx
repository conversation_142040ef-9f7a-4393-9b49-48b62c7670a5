'use client'

import React, { useState } from 'react'
import { useAppStore, useActiveTab } from '@/lib/store'
import LightBrowser from '@/components/ui/LightBrowser'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import { WelcomeScreen } from '@/components/WelcomeScreen'
// import { ContentViewer } from '@/components/ContentViewer'
import PureContentViewer from '@/components/ui/PureContentViewer'
// import SimpleDiffViewer from '@/components/ui/SimpleDiffViewer'

const WorkArea: React.FC = () => {
  const {
    tabs,
    addTab,
    setProcessing
  } = useAppStore()
  const activeTab = useActiveTab()
  const [inputValue, setInputValue] = useState('')

  // 生成智能标题
  const generateTitle = async (content: string, contentType: 'url' | 'text', webTitle?: string) => {
    try {
      const response = await fetch('/api/generate-title', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          contentType,
          webTitle
        }),
      })

      if (response.ok) {
        const data = await response.json()
        return data.title || (contentType === 'url' ? '网页内容' : '文本内容')
      }
    } catch (error) {
      console.error('生成标题失败:', error)
    }

    // 备用标题
    return contentType === 'url' ? '网页内容' : '文本内容'
  }

  // 处理输入提交 - 集成智能标题生成
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim()) return

    setProcessing(true)

    try {
      // 判断输入类型并规范化URL
      let processedInput = inputValue.trim()
      let isUrl = false

      // 检测和规范化URL
      if (processedInput.startsWith('http://') || processedInput.startsWith('https://')) {
        isUrl = true
      } else if (processedInput.includes('.') && !processedInput.includes(' ') && processedInput.length > 3) {
        // 可能是没有协议的URL，自动添加https://
        processedInput = 'https://' + processedInput
        isUrl = true
      }

      if (isUrl) {
        // 对于URL：先创建标签页，然后异步生成标题
        const tabId = addTab({
          title: new URL(processedInput).hostname, // 临时标题
          sourceType: 'url',
          sourceData: processedInput,
          originalContent: '',
          aiNoteMarkdown: '',
          isLoading: false
        })

        // 异步生成智能标题
        generateTitle(processedInput, 'url').then(title => {
          const { updateTab } = useAppStore.getState()
          updateTab(tabId, { title })
        })

        setInputValue('')
        setProcessing(false)
      } else {
        // 对于文本：先创建标签页，然后异步生成标题
        const tabId = addTab({
          title: '文本内容', // 临时标题
          sourceType: 'text',
          sourceData: inputValue,
          originalContent: inputValue,
          aiNoteMarkdown: '',
          isLoading: false
        })

        // 异步生成智能标题
        generateTitle(inputValue, 'text').then(title => {
          const { updateTab } = useAppStore.getState()
          updateTab(tabId, { title })
        })

        setInputValue('')
        setProcessing(false)
      }
    } catch (error) {
      console.error('处理错误:', error)
      setProcessing(false)
      // 可以在这里显示错误提示
    }
  }

  // 移除了AI分析相关函数，沉淀模式专注于内容阅读

  // 直接处理输入的函数 - 集成智能标题生成
  const handleSubmitDirect = async (input: string) => {
    if (!input.trim()) return

    setProcessing(true)

    try {
      // 判断输入类型并规范化URL
      let processedInput = input.trim()
      let isUrl = false

      // 检测和规范化URL
      if (processedInput.startsWith('http://') || processedInput.startsWith('https://')) {
        isUrl = true
      } else if (processedInput.includes('.') && !processedInput.includes(' ') && processedInput.length > 3) {
        // 可能是没有协议的URL，自动添加https://
        processedInput = 'https://' + processedInput
        isUrl = true
      }

      if (isUrl) {
        // 对于URL：先创建标签页，然后异步生成标题
        const tabId = addTab({
          title: new URL(processedInput).hostname, // 临时标题
          sourceType: 'url',
          sourceData: processedInput,
          originalContent: '',
          aiNoteMarkdown: '',
          isLoading: false
        })

        // 异步生成智能标题
        generateTitle(processedInput, 'url').then(title => {
          const { updateTab } = useAppStore.getState()
          updateTab(tabId, { title })
        })

        setInputValue('')
        setProcessing(false)
      } else {
        // 对于文本：先创建标签页，然后异步生成标题
        const tabId = addTab({
          title: '文本内容', // 临时标题
          sourceType: 'text',
          sourceData: input,
          originalContent: input,
          aiNoteMarkdown: '',
          isLoading: false
        })

        // 异步生成智能标题
        generateTitle(input, 'text').then(title => {
          const { updateTab } = useAppStore.getState()
          updateTab(tabId, { title })
        })

        setInputValue('')
        setProcessing(false)
      }
    } catch (error) {
      console.error('处理错误:', error)
      setProcessing(false)
      // 可以在这里显示错误提示
    }
  }

  // 移除了拖拽相关代码，沉淀模式使用固定布局

  // 如果没有标签页，显示新的欢迎界面
  if (tabs.length === 0) {
    return (
      <WelcomeScreen
        onSubmit={(input) => {
          setInputValue(input)
          // 直接调用处理函数，避免事件模拟
          handleSubmitDirect(input)
        }}
        loading={useAppStore.getState().isProcessing}
      />
    )
  }

  // 移除了原文内容和结构化笔记的渲染函数，沉淀模式统一使用PureContentViewer

  // 渲染标签页内容 - 沉淀模式专用
  const renderTabContent = () => {
    if (!activeTab || !activeTab.sourceData) {
      return (
        <WelcomeScreen
          onSubmit={(input) => {
            setInputValue(input)
            handleSubmitDirect(input)
          }}
          loading={useAppStore.getState().isProcessing}
        />
      )
    }

    if (activeTab.isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">正在处理内容...</p>
          </div>
        </div>
      )
    }

    // 沉淀模式：使用纯净的内容查看器，专注于阅读体验
    return <PureContentViewer activeTab={activeTab} />
  }

  return (
    <div className="h-full flex flex-col relative">
      <div className="flex-1 overflow-hidden">
        {renderTabContent()}
      </div>
    </div>
  )
}

export default WorkArea
