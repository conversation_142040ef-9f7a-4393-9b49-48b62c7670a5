'use client'

import React, { useEffect } from 'react'
import { useAppStore } from '@/lib/store'
import { X, Plus, FileText } from 'lucide-react'
import WorkArea from './WorkArea'
import DynamicBackground from '../ui/DynamicBackground'
import { cn } from '@/lib/utils'

const MainLayout: React.FC = () => {
  const {
    tabs
  } = useAppStore()

  // 移除了键盘快捷键和拖拽相关逻辑，沉淀模式专注于简洁的阅读体验

  // 判断当前活跃标签页是否为空的新建标签页
  const activeTab = tabs.find(tab => tab.id === useAppStore.getState().activeTabId)
  const isEmptyNewTab = activeTab &&
    activeTab.sourceData === '' &&
    activeTab.originalContent === '' &&
    activeTab.aiNoteMarkdown === ''

  return (
    <div className="h-screen adaptive-light flex flex-col relative pure-mode">
      {/* 动态背景 - 只在没有标签页或空标签页时显示 */}
      {(tabs.length === 0 || isEmptyNewTab) && <DynamicBackground />}

      {/* 动态标签栏 - 只在有标签页时显示 */}
      {tabs.length > 0 && (
        <div className="glass-effect-strong border-b border-border/50 flex items-center px-6 shadow-sm flex-shrink-0">
          {/* Chrome风格标签页布局 */}
          <div className="flex items-center">
            {/* 标签页列表 - 紧密排列 */}
            <div className="flex items-center overflow-x-auto">
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-t-xl cursor-pointer transition-all duration-200 flex-shrink-0 ${
                    tab.id === useAppStore.getState().activeTabId
                      ? 'glass-effect text-primary border-b-2 border-primary shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                  }`}
                  onClick={() => useAppStore.getState().setActiveTab(tab.id)}
                >
                  <span className="text-sm font-medium truncate max-w-32">
                    {tab.title}
                  </span>
                  {tab.isLoading && (
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  )}
                  {tab.aiAnalyzing && (
                    <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                  )}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      useAppStore.getState().removeTab(tab.id)
                    }}
                    className="p-1 hover:bg-gray-200/80 rounded-full transition-colors"
                  >
                    <X size={14} />
                  </button>
                </div>
              ))}
            </div>

            {/* Chrome风格新建标签页按钮 - 紧贴最右侧标签页 */}
            <button
              onClick={() => {
                useAppStore.getState().addTab({
                  title: '新标签页',
                  sourceType: 'text',
                  sourceData: '',
                  originalContent: '',
                  aiNoteMarkdown: '',
                  isLoading: false
                })
              }}
              className="ml-1 p-2 hover:bg-white/60 rounded-lg transition-all duration-200 flex-shrink-0"
              title="新建标签页"
            >
              <Plus size={14} className="text-gray-500" />
            </button>
          </div>


        </div>
      )}

      {/* 主要内容区域 - 单栏布局 */}
      <div className="flex-1 flex overflow-hidden min-h-0 p-2">

        {/* 中栏 - 多Tab内容区 */}
        <div
          className="flex flex-col min-h-0 liquid-panel"
          style={{
            width: '100%'
          }}
        >
          {/* 移除了标签页切换，沉淀模式专注于内容阅读 */}
          
          <WorkArea />
        </div>

        {/* 沉淀模式：专注于纯净的内容阅读体验 */}
      </div>
    </div>
  )
}

export default MainLayout
