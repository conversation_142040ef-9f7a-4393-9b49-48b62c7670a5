'use client'

import React, { useState, useMemo } from 'react'
import { ChevronDown, ChevronUp, MessageSquare, BookOpen, Lightbulb, Target, Hash } from 'lucide-react'
import { cn } from '@/lib/utils'
import { NoteReference } from '@/lib/store'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

interface BentoNoteViewProps {
  noteContent: string
  noteReferences?: NoteReference[]
  onSegmentClick?: (segmentId: string) => void
}

interface NoteSection {
  id: string
  type: 'summary' | 'outline' | 'keypoints' | 'insights' | 'custom'
  title: string
  content: string
  icon: React.ReactNode
  color: string
  bgColor: string
}

const BentoNoteView: React.FC<BentoNoteViewProps> = ({
  noteContent,
  noteReferences = [],
  onSegmentClick
}) => {
  const [expandedCards, setExpandedCards] = useState<Record<string, boolean>>({})
  const [hoveredCard, setHoveredCard] = useState<string | null>(null)
  const [expandedOriginals, setExpandedOriginals] = useState<Record<string, boolean>>({})

  // 解析 Markdown 内容为结构化部分
  const sections = useMemo(() => {
    const parsedSections: NoteSection[] = []
    
    // 简单的 Markdown 解析逻辑
    const lines = noteContent.split('\n')
    let currentSection: Partial<NoteSection> | null = null
    let contentBuffer: string[] = []

    lines.forEach((line, index) => {
      // 检测一级标题
      if (line.startsWith('# ')) {
        if (currentSection && contentBuffer.length > 0) {
          currentSection.content = contentBuffer.join('\n').trim()
          parsedSections.push(currentSection as NoteSection)
        }
        
        const title = line.substring(2).trim()
        currentSection = {
          id: `section-${index}`,
          title,
          content: '',
          ...getSectionStyle(title)
        }
        contentBuffer = []
      } else if (currentSection) {
        contentBuffer.push(line)
      }
    })

    // 添加最后一个部分
    if (currentSection && contentBuffer.length > 0) {
      currentSection.content = contentBuffer.join('\n').trim()
      parsedSections.push(currentSection as NoteSection)
    }

    // 如果没有解析到任何部分，创建一个默认部分
    if (parsedSections.length === 0 && noteContent.trim()) {
      parsedSections.push({
        id: 'default-section',
        type: 'custom',
        title: '笔记内容',
        content: noteContent,
        icon: <BookOpen size={20} />,
        color: 'text-gray-700',
        bgColor: 'bg-gray-50'
      })
    }

    return parsedSections
  }, [noteContent])

  // 根据标题获取部分样式
  function getSectionStyle(title: string): Partial<NoteSection> {
    const lowerTitle = title.toLowerCase()
    
    if (lowerTitle.includes('摘要') || lowerTitle.includes('总结') || lowerTitle.includes('概述')) {
      return {
        type: 'summary',
        icon: <BookOpen size={20} />,
        color: 'text-blue-700',
        bgColor: 'bg-blue-50'
      }
    } else if (lowerTitle.includes('大纲') || lowerTitle.includes('结构') || lowerTitle.includes('目录')) {
      return {
        type: 'outline',
        icon: <Hash size={20} />,
        color: 'text-purple-700',
        bgColor: 'bg-purple-50'
      }
    } else if (lowerTitle.includes('要点') || lowerTitle.includes('关键') || lowerTitle.includes('核心')) {
      return {
        type: 'keypoints',
        icon: <Target size={20} />,
        color: 'text-green-700',
        bgColor: 'bg-green-50'
      }
    } else if (lowerTitle.includes('见解') || lowerTitle.includes('思考') || lowerTitle.includes('启发')) {
      return {
        type: 'insights',
        icon: <Lightbulb size={20} />,
        color: 'text-orange-700',
        bgColor: 'bg-orange-50'
      }
    } else {
      return {
        type: 'custom',
        icon: <MessageSquare size={20} />,
        color: 'text-gray-700',
        bgColor: 'bg-gray-50'
      }
    }
  }

  // 处理卡片展开/折叠
  const toggleCard = (cardId: string) => {
    setExpandedCards(prev => ({
      ...prev,
      [cardId]: !prev[cardId]
    }))
  }

  // 处理原文摘录展开
  const toggleOriginal = (sectionId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setExpandedOriginals(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }))
  }

  // 获取与某个部分相关的原文引用
  const getReferencesForSection = (sectionContent: string): NoteReference[] => {
    return noteReferences.filter(ref => 
      sectionContent.includes(ref.noteContent)
    )
  }

  return (
    <div className="p-6 space-y-4">
      {/* Bento 网格布局 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {sections.map((section) => {
          const isExpanded = expandedCards[section.id] || false
          const isHovered = hoveredCard === section.id
          const hasOriginal = expandedOriginals[section.id]
          const references = getReferencesForSection(section.content)

          return (
            <div
              key={section.id}
              className={cn(
                "group relative rounded-xl border-2 transition-all duration-300",
                section.bgColor,
                isExpanded ? "md:col-span-2" : "",
                isHovered ? "shadow-lg scale-[1.02]" : "shadow-sm",
                "cursor-pointer"
              )}
              onMouseEnter={() => setHoveredCard(section.id)}
              onMouseLeave={() => setHoveredCard(null)}
              onClick={() => toggleCard(section.id)}
            >
              {/* 卡片头部 */}
              <div className="p-4 flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className={cn("mt-0.5", section.color)}>
                    {section.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className={cn("font-semibold text-lg", section.color)}>
                      {section.title}
                    </h3>
                  </div>
                </div>
                <button
                  className={cn(
                    "p-1 rounded-lg transition-colors",
                    section.color,
                    "hover:bg-white/50"
                  )}
                  onClick={(e) => {
                    e.stopPropagation()
                    toggleCard(section.id)
                  }}
                >
                  {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                </button>
              </div>

              {/* 卡片内容 */}
              <div className={cn(
                "px-4 pb-4 overflow-hidden transition-all duration-300",
                isExpanded ? "max-h-none" : "max-h-32"
              )}>
                <div className="prose prose-sm max-w-none">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {section.content}
                  </ReactMarkdown>
                </div>

                {/* 查看原文按钮 */}
                {references.length > 0 && isHovered && (
                  <button
                    className={cn(
                      "mt-3 text-sm flex items-center space-x-1 transition-opacity",
                      section.color,
                      "hover:underline"
                    )}
                    onClick={(e) => toggleOriginal(section.id, e)}
                  >
                    <span>🡳</span>
                    <span>查看原文</span>
                  </button>
                )}
              </div>

              {/* 原文摘录卡片 */}
              {hasOriginal && references.length > 0 && (
                <div className="mx-4 mb-4 p-3 bg-white/70 rounded-lg border border-gray-200 shadow-inner">
                  <div className="text-xs text-gray-500 mb-2">原文摘录</div>
                  <div className="space-y-2">
                    {references.map((ref, idx) => (
                      <div
                        key={idx}
                        className="p-2 bg-gray-50 rounded text-sm text-gray-700 cursor-pointer hover:bg-gray-100"
                        onClick={(e) => {
                          e.stopPropagation()
                          if (onSegmentClick && ref.sourceSegments[0]) {
                            onSegmentClick(ref.sourceSegments[0])
                          }
                        }}
                      >
                        {ref.noteContent}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 快速操作按钮 */}
              {isExpanded && (
                <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    className={cn(
                      "p-2 rounded-lg bg-white/70 shadow-sm",
                      section.color,
                      "hover:bg-white"
                    )}
                    onClick={(e) => {
                      e.stopPropagation()
                      // TODO: 发送到AI助手
                      console.log('Send to AI:', section.content)
                    }}
                    title="发送到AI助手"
                  >
                    <MessageSquare size={16} />
                  </button>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* 底部操作栏 */}
      {expandedOriginals && Object.keys(expandedOriginals).some(key => expandedOriginals[key]) && (
        <div className="fixed bottom-4 right-4 z-10">
          <button
            className="px-4 py-2 bg-primary text-white rounded-lg shadow-lg hover:bg-primary/90 transition-colors"
            onClick={() => {
              // TODO: 定位所有高亮
              console.log('Locate all highlights')
            }}
          >
            定位全部
          </button>
        </div>
      )}
    </div>
  )
}

export default BentoNoteView