'use client'

import React, { useEffect, useRef, useState } from 'react'
import { cn } from '@/lib/utils'

interface OriginalContentViewerProps {
  content: string
  highlightedSegments?: string[]
  onSegmentClick?: (segmentId: string) => void
}

const OriginalContentViewer: React.FC<OriginalContentViewerProps> = ({
  content,
  highlightedSegments = [],
  onSegmentClick
}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const [highlightTimers, setHighlightTimers] = useState<Record<string, NodeJS.Timeout>>({})

  // 处理高亮效果
  useEffect(() => {
    if (!contentRef.current || highlightedSegments.length === 0) return

    // 清除之前的高亮定时器
    Object.values(highlightTimers).forEach(timer => clearTimeout(timer))
    setHighlightTimers({})

    highlightedSegments.forEach(segmentId => {
      const element = contentRef.current?.querySelector(`[data-segment-id="${segmentId}"]`)
      if (element) {
        // 添加高亮
        element.classList.add('highlight-active')
        
        // 滚动到视图
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })

        // 3秒后渐隐
        const timer = setTimeout(() => {
          element.classList.remove('highlight-active')
        }, 3000)

        setHighlightTimers(prev => ({
          ...prev,
          [segmentId]: timer
        }))
      }
    })

    return () => {
      Object.values(highlightTimers).forEach(timer => clearTimeout(timer))
    }
  }, [highlightedSegments])

  // 解析内容为段落
  const renderContent = () => {
    const paragraphs = content.split('\n\n').filter(p => p.trim())
    
    return paragraphs.map((paragraph, index) => {
      const segmentId = `para-${index}`
      const isHighlighted = highlightedSegments.includes(segmentId)
      
      return (
        <p
          key={index}
          data-segment-id={segmentId}
          className={cn(
            "mb-4 text-gray-700 leading-relaxed cursor-pointer transition-all duration-300",
            "hover:bg-gray-100/50 p-2 rounded -mx-2",
            isHighlighted && "highlight-active"
          )}
          onClick={() => onSegmentClick?.(segmentId)}
        >
          {paragraph}
        </p>
      )
    })
  }

  return (
    <div ref={contentRef} className="prose prose-sm max-w-none">
      <style jsx>{`
        .highlight-active {
          background-color: rgba(255, 235, 59, 0.5);
          animation: highlight-fade 3s ease-out;
        }
        
        @keyframes highlight-fade {
          0% {
            background-color: rgba(255, 235, 59, 0.5);
          }
          70% {
            background-color: rgba(255, 235, 59, 0.5);
          }
          100% {
            background-color: transparent;
          }
        }
      `}</style>
      
      {renderContent()}
    </div>
  )
}

export default OriginalContentViewer