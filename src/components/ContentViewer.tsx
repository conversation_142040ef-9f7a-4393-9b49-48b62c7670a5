'use client'

import { Download, Share2 } from 'lucide-react'
import SafeMarkdown from './ui/SafeMarkdown'

interface ProcessedContent {
  originalContent: string
  title: string
  cached?: boolean
}

interface ContentViewerProps {
  content: ProcessedContent | string | null
  loading: boolean
}

function LoadingState() {
  return (
    <div className="flex flex-col items-center justify-center h-96 space-y-4">
      <div className="flex space-x-2">
        <div className="w-3 h-3 bg-primary rounded-full dot-flashing"></div>
        <div className="w-3 h-3 bg-primary rounded-full dot-flashing"></div>
        <div className="w-3 h-3 bg-primary rounded-full dot-flashing"></div>
      </div>
      <p className="text-muted-foreground">正在解析内容...</p>
    </div>
  )
}

export function ContentViewer({ content, loading }: ContentViewerProps) {
  // 处理字符串内容
  const processedContent: ProcessedContent | null = typeof content === 'string' 
    ? { originalContent: content, title: '', cached: false }
    : content

  // 检查内容是否为HTML格式
  const isHtmlContent = (content: string) => {
    return /<[^>]+>/.test(content)
  }



  if (loading) {
    return (
      <div className="h-full bg-white rounded-lg border border-border">
        <LoadingState />
      </div>
    )
  }

  if (!processedContent) {
    return (
      <div className="h-full bg-white rounded-lg border border-border flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-6xl text-muted-foreground/30">📖</div>
          <div>
            <p className="text-lg font-medium text-muted-foreground">内容将在这里显示</p>
            <p className="text-sm text-muted-foreground/60 mt-2">
              输入链接或粘贴文章开始阅读
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col liquid-glass">
      {/* 顶部工具栏 */}
      {processedContent.title && (
        <div className="p-4 border-b border-gray-200/50 bg-white/50 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold text-gray-900 leading-tight">
                {processedContent.title}
              </h1>
              {processedContent.cached && (
                <div className="flex items-center mt-1">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    ⚡ 缓存加载
                  </span>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <button className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200">
                <Download className="w-4 h-4 mr-1.5 inline" />
                导出
              </button>
              <button className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200">
                <Share2 className="w-4 h-4 mr-1.5 inline" />
                分享
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 内容区域 - 增强滚动功能 */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin" style={{ height: 'calc(100vh - 200px)' }}>
        <div className="p-6 min-h-full">
          {isHtmlContent(processedContent.originalContent) ? (
            // HTML内容使用dangerouslySetInnerHTML
            <div
              className="prose prose-slate max-w-none
                prose-headings:text-gray-900 prose-headings:font-semibold prose-headings:tracking-tight
                prose-h1:text-xl prose-h1:mb-4 prose-h1:mt-0 prose-h1:border-b prose-h1:border-gray-200 prose-h1:pb-3 prose-h1:leading-tight
                prose-h2:text-lg prose-h2:mb-3 prose-h2:mt-6 prose-h2:border-b prose-h2:border-gray-200 prose-h2:pb-2 prose-h2:leading-tight
                prose-h3:text-base prose-h3:mb-2 prose-h3:mt-4 prose-h3:leading-tight
                prose-h4:text-sm prose-h4:mb-2 prose-h4:mt-3 prose-h4:leading-tight
                prose-p:text-gray-900 prose-p:leading-relaxed prose-p:mb-3 prose-p:text-sm
                prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline prose-a:font-medium prose-a:transition-colors
                prose-strong:text-gray-900 prose-strong:font-semibold
                prose-em:text-gray-900 prose-em:italic
                prose-ul:text-gray-900 prose-ol:text-gray-900 prose-ul:space-y-1 prose-ol:space-y-1
                prose-li:text-gray-900 prose-li:mb-1 prose-li:leading-relaxed
                prose-blockquote:text-gray-600 prose-blockquote:border-l-4 prose-blockquote:border-blue-200 prose-blockquote:pl-6 prose-blockquote:italic prose-blockquote:bg-gray-50 prose-blockquote:py-2 prose-blockquote:rounded-r
                prose-code:text-blue-600 prose-code:bg-gray-100 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm prose-code:font-mono prose-code:border prose-code:border-gray-200
                prose-pre:bg-gray-100 prose-pre:border prose-pre:border-gray-200 prose-pre:rounded-lg prose-pre:p-4 prose-pre:overflow-x-auto
                prose-img:rounded-lg prose-img:shadow-md prose-img:border prose-img:border-gray-200 prose-img:max-w-full prose-img:h-auto
                prose-hr:border-gray-200 prose-hr:my-8 prose-hr:border-t-2
                prose-table:border-collapse prose-table:border prose-table:border-gray-200 prose-table:rounded-lg prose-table:overflow-hidden
                prose-th:border prose-th:border-gray-200 prose-th:bg-gray-100 prose-th:p-3 prose-th:font-semibold prose-th:text-left
                prose-td:border prose-td:border-gray-200 prose-td:p-3 prose-td:align-top
                [&>*:first-child]:mt-0
                [&>*:last-child]:mb-0"
              dangerouslySetInnerHTML={{ __html: processedContent.originalContent }}
            />
          ) : (
            // 纯文本内容使用SafeMarkdown
            <SafeMarkdown
              className="prose prose-slate max-w-none
                prose-headings:text-gray-900 prose-headings:font-semibold prose-headings:tracking-tight
                prose-h1:text-xl prose-h1:mb-4 prose-h1:mt-0 prose-h1:border-b prose-h1:border-gray-200 prose-h1:pb-3 prose-h1:leading-tight
                prose-h2:text-lg prose-h2:mb-3 prose-h2:mt-6 prose-h2:border-b prose-h2:border-gray-200 prose-h2:pb-2 prose-h2:leading-tight
                prose-h3:text-base prose-h3:mb-2 prose-h3:mt-4 prose-h3:leading-tight
                prose-h4:text-sm prose-h4:mb-2 prose-h4:mt-3 prose-h4:leading-tight
                prose-p:text-gray-900 prose-p:leading-relaxed prose-p:mb-3 prose-p:text-sm
                prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline prose-a:font-medium prose-a:transition-colors
                prose-strong:text-gray-900 prose-strong:font-semibold
                prose-em:text-gray-900 prose-em:italic
                prose-ul:text-gray-900 prose-ol:text-gray-900 prose-ul:space-y-1 prose-ol:space-y-1
                prose-li:text-gray-900 prose-li:mb-1 prose-li:leading-relaxed
                prose-blockquote:text-gray-600 prose-blockquote:border-l-4 prose-blockquote:border-blue-200 prose-blockquote:pl-6 prose-blockquote:italic prose-blockquote:bg-gray-50 prose-blockquote:py-2 prose-blockquote:rounded-r
                prose-code:text-blue-600 prose-code:bg-gray-100 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm prose-code:font-mono prose-code:border prose-code:border-gray-200
                prose-pre:bg-gray-100 prose-pre:border prose-pre:border-gray-200 prose-pre:rounded-lg prose-pre:p-4 prose-pre:overflow-x-auto
                prose-img:rounded-lg prose-img:shadow-md prose-img:border prose-img:border-gray-200 prose-img:max-w-full prose-img:h-auto
                prose-hr:border-gray-200 prose-hr:my-8 prose-hr:border-t-2
                prose-table:border-collapse prose-table:border prose-table:border-gray-200 prose-table:rounded-lg prose-table:overflow-hidden
                prose-th:border prose-th:border-gray-200 prose-th:bg-gray-100 prose-th:p-3 prose-th:font-semibold prose-th:text-left
                prose-td:border prose-td:border-gray-200 prose-td:p-3 prose-td:align-top
                [&>*:first-child]:mt-0
                [&>*:last-child]:mb-0"
            >
              {processedContent.originalContent}
            </SafeMarkdown>
          )}
        </div>
      </div>
    </div>
  )
} 