'use client'

import { useState } from 'react'
// import { Zap } from 'lucide-react'
import DynamicBackground from './ui/DynamicBackground'
import DataFlowIcon from './ui/DataFlowIcon'
import QuickActions from './ui/QuickActions'

interface WelcomeScreenProps {
  onSubmit: (input: string) => void
  loading: boolean
}

export function WelcomeScreen({ onSubmit, loading }: WelcomeScreenProps) {
  const [input, setInput] = useState('')

  const handleSubmit = () => {
    if (input.trim() && !loading) {
      onSubmit(input.trim())
      setInput('') // 清空输入框避免重复提交
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !loading) {
      e.preventDefault()
      handleSubmit()
    }
    // Shift+Enter 允许换行，不做任何处理
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* 动态背景 */}
      <DynamicBackground />

      {/* 主容器 */}
      <div className="relative z-10 h-screen flex flex-col items-center justify-center text-center px-8">
        {/* 应用图标 */}
        <div className="mb-8">
          <DataFlowIcon onClick={() => console.log('图标点击')} />
        </div>

        {/* 标题 */}
        <h1 className="app-title mb-4">沉淀</h1>
        <p className="app-subtitle mb-12">智能内容分析与结构化笔记生成</p>

        {/* 输入区域 */}
        <div className="w-full max-w-2xl mb-8">
          <div className="input-wrapper">
            <textarea
              className="input-field resize-none"
              placeholder="输入网页链接或粘贴文本内容..."
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={loading}
              rows={3}
              style={{ minHeight: '60px', maxHeight: '200px' }}
            />
            <svg
              className="input-icon"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              onClick={handleSubmit}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
          </div>
        </div>

        {/* 主操作按钮 */}
        <button
          className="action-button"
          onClick={handleSubmit}
          disabled={!input.trim() || loading}
        >
          {loading ? '分析中...' : '开始分析'}
        </button>
      </div>

      {/* 快捷功能 */}
      <QuickActions />
    </div>
  )
}