<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库 - 个人笔记系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-glass: rgba(255, 255, 255, 0.7);
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --accent: #6366f1;
            --accent-light: #818cf8;
            --border: rgba(226, 232, 240, 0.5);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.05), 0 10px 10px -5px rgba(0, 0, 0, 0.02);
            --blur: 20px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 背景装饰 */
        .bg-decoration {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: -1;
            overflow: hidden;
        }

        .bg-circle {
            position: absolute;
            border-radius: 50%;
            filter: blur(100px);
            opacity: 0.5;
            animation: float 20s infinite ease-in-out;
        }

        .bg-circle:nth-child(1) {
            width: 400px;
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            top: -200px;
            right: -100px;
        }

        .bg-circle:nth-child(2) {
            width: 300px;
            height: 300px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            bottom: -150px;
            left: -50px;
            animation-delay: -5s;
        }

        .bg-circle:nth-child(3) {
            width: 250px;
            height: 250px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            top: 50%;
            left: 50%;
            animation-delay: -10s;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            25% { transform: translate(-30px, 30px) scale(1.05); }
            50% { transform: translate(30px, -30px) scale(0.95); }
            75% { transform: translate(-20px, -20px) scale(1.02); }
        }

        /* 布局容器 */
        .app-container {
            display: flex;
            height: 100vh;
            position: relative;
        }

        /* 侧边栏 */
        .sidebar {
            width: 280px;
            background: var(--bg-glass);
            backdrop-filter: blur(var(--blur));
            -webkit-backdrop-filter: blur(var(--blur));
            border-right: 1px solid var(--border);
            padding: 2rem;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, var(--accent) 0%, var(--accent-light) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .search-box {
            position: relative;
            margin-bottom: 2rem;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid var(--border);
            border-radius: 12px;
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            opacity: 0.5;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-title {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--text-secondary);
            margin-bottom: 0.75rem;
            letter-spacing: 0.05em;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            margin-bottom: 0.25rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            color: var(--text-primary);
        }

        .nav-item:hover {
            background: rgba(99, 102, 241, 0.1);
            transform: translateX(4px);
        }

        .nav-item.active {
            background: var(--accent);
            color: white;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 0.75rem;
            opacity: 0.7;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            padding: 2rem 3rem;
            overflow-y: auto;
        }

        .content-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        /* Bento Grid 布局 */
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .bento-card {
            background: var(--bg-glass);
            backdrop-filter: blur(var(--blur));
            -webkit-backdrop-filter: blur(var(--blur));
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            position: relative;
            overflow: hidden;
        }

        .bento-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--accent);
        }

        .bento-card.large {
            grid-column: span 2;
        }

        .bento-card.tall {
            grid-row: span 2;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .card-meta {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .tag {
            padding: 0.25rem 0.75rem;
            background: rgba(99, 102, 241, 0.1);
            color: var(--accent);
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .card-content {
            color: var(--text-secondary);
            line-height: 1.8;
            font-size: 0.95rem;
        }

        /* 笔记内容区 */
        .note-view {
            background: var(--bg-glass);
            backdrop-filter: blur(var(--blur));
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 3rem;
            margin-top: 2rem;
            box-shadow: var(--shadow);
        }

        .note-content {
            font-size: 1.05rem;
            line-height: 1.8;
            color: var(--text-primary);
        }

        .note-content h2 {
            margin: 2rem 0 1rem;
            font-size: 1.75rem;
            font-weight: 600;
        }

        .note-content h3 {
            margin: 1.5rem 0 0.75rem;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .note-content p {
            margin-bottom: 1.25rem;
        }

        .note-content blockquote {
            border-left: 4px solid var(--accent);
            padding-left: 1.5rem;
            margin: 1.5rem 0;
            color: var(--text-secondary);
            font-style: italic;
        }

        .note-content code {
            background: rgba(99, 102, 241, 0.1);
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        /* 快捷操作按钮 */
        .fab {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 56px;
            height: 56px;
            background: var(--accent);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            border: none;
            font-size: 1.5rem;
        }

        .fab:hover {
            transform: scale(1.1);
            background: var(--accent-light);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 240px;
                position: absolute;
                left: -240px;
                z-index: 100;
                height: 100%;
            }

            .sidebar.open {
                left: 0;
            }

            .main-content {
                padding: 1.5rem;
            }

            .bento-grid {
                grid-template-columns: 1fr;
            }

            .bento-card.large {
                grid-column: span 1;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(99, 102, 241, 0.3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(99, 102, 241, 0.5);
        }
    </style>
</head>
<body>
    <!-- 背景装饰 -->
    <div class="bg-decoration">
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
    </div>

    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="logo">知识库</div>
            
            <div class="search-box">
                <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <input type="text" class="search-input" placeholder="搜索笔记...">
            </div>

            <nav>
                <div class="nav-section">
                    <div class="nav-title">快速访问</div>
                    <a href="#" class="nav-item active">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        主页
                    </a>
                    <a href="#" class="nav-item">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                        收藏
                    </a>
                    <a href="#" class="nav-item">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        最近
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-title">分类</div>
                    <a href="#" class="nav-item">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        想法
                    </a>
                    <a href="#" class="nav-item">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        读书笔记
                    </a>
                    <a href="#" class="nav-item">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                        技术文档
                    </a>
                    <a href="#" class="nav-item">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                        项目管理
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-title">标签</div>
                    <a href="#" class="nav-item">
                        <span class="nav-icon">#</span>
                        产品设计
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-icon">#</span>
                        用户体验
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-icon">#</span>
                        前端开发
                    </a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <div class="content-header">
                <h1 class="page-title">我的知识库</h1>
                <p class="page-subtitle">整理思维，积累知识，创造价值</p>
            </div>

            <!-- Bento Grid 布局 -->
            <div class="bento-grid">
                <div class="bento-card large">
                    <div class="card-header">
                        <h3 class="card-title">设计系统构建指南</h3>
                    </div>
                    <div class="card-meta">
                        <span class="tag">设计</span>
                        <span class="tag">系统</span>
                        <span class="tag">组件库</span>
                    </div>
                    <div class="card-content">
                        探索如何从零开始构建一个完整的设计系统，包括设计原则、组件规范、设计令牌等核心要素。本文将深入讨论设计系统的价值、实施策略以及维护方法...
                    </div>
                </div>

                <div class="bento-card">
                    <div class="card-header">
                        <h3 class="card-title">React 18 新特性</h3>
                    </div>
                    <div class="card-meta">
                        <span class="tag">React</span>
                        <span class="tag">前端</span>
                    </div>
                    <div class="card-content">
                        React 18 带来了许多激动人心的新特性，包括并发渲染、自动批处理、Suspense 改进等...
                    </div>
                </div>

                <div class="bento-card">
                    <div class="card-header">
                        <h3 class="card-title">用户研究方法论</h3>
                    </div>
                    <div class="card-meta">
                        <span class="tag">UX</span>
                        <span class="tag">研究</span>
                    </div>
                    <div class="card-content">
                        深入了解各种用户研究方法，包括访谈、问卷调查、可用性测试等，以及如何选择合适的研究方法...
                    </div>
                </div>

                <div class="bento-card tall">
                    <div class="card-header">
                        <h3 class="card-title">产品思维模型</h3>
                    </div>
                    <div class="card-meta">
                        <span class="tag">产品</span>
                        <span class="tag">思维</span>
                        <span class="tag">方法论</span>
                    </div>
                    <div class="card-content">
                        <p>整理了常用的产品思维模型：</p>
                        <ul style="margin-left: 1.5rem; margin-top: 1rem;">
                            <li>AARRR 模型</li>
                            <li>KANO 模型</li>
                            <li>用户故事地图</li>
                            <li>双钻模型</li>
                            <li>5W2H 分析法</li>
                        </ul>
                        <p style="margin-top: 1rem;">每个模型都有其适用场景和局限性，需要根据具体情况灵活运用...</p>
                    </div>
                </div>

                <div class="bento-card">
                    <div class="card-header">
                        <h3 class="card-title">TypeScript 最佳实践</h3>
                    </div>
                    <div class="card-meta">
                        <span class="tag">TypeScript</span>
                        <span class="tag">编程</span>
                    </div>
                    <div class="card-content">
                        总结了在实际项目中使用 TypeScript 的最佳实践，包括类型设计、工具配置、性能优化等方面...
                    </div>
                </div>
            </div>

            <!-- 笔记详情视图示例 -->
            <div class="note-view">
                <h1 class="page-title">现代前端架构设计</h1>
                <div class="card-meta" style="margin: 1rem 0;">
                    <span class="tag">架构</span>
                    <span class="tag">前端</span>
                    <span class="tag">工程化</span>
                </div>
                
                <div class="note-content">
                    <p>在现代前端开发中，良好的架构设计是项目成功的关键。一个优秀的前端架构不仅能够提高开发效率，还能确保应用的可维护性和可扩展性。</p>
                    
                    <h2>核心原则</h2>
                    <p>构建现代前端架构时，我们需要遵循以下几个核心原则：</p>
                    
                    <blockquote>
                        "好的架构让改变变得容易，而糟糕的架构让改变变得困难。"
                    </blockquote>
                    
                    <h3>1. 关注点分离</h3>
                    <p>将应用的不同关注点进行分离，比如将业务逻辑、UI 组件、数据管理等分别处理。这样可以降低各部分之间的耦合度，提高代码的可维护性。</p>
                    
                    <h3>2. 组件化设计</h3>
                    <p>采用组件化的方式构建 UI，每个组件都应该是独立的、可复用的单元。通过 <code>props</code> 和 <code>events</code> 进行组件间通信，避免直接的依赖关系。</p>
                    
                    <h3>3. 状态管理</h3>
                    <p>对于复杂应用，合理的状态管理策略至关重要。可以选择 Redux、MobX、Zustand 等状态管理库，或者使用 React Context API 配合 useReducer 来管理应用状态。</p>
                </div>
            </div>
        </main>
    </div>

    <!-- 浮动操作按钮 -->
    <button class="fab">+</button>

    <script>
        // 添加一些交互效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 搜索框聚焦效果
        const searchInput = document.querySelector('.search-input');
        searchInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        searchInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });

        // 卡片点击效果
        document.querySelectorAll('.bento-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 100);
            });
        });
    </script>
</body>
</html>